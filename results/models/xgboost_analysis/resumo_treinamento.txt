RESUMO DO TREINAMENTO XGBOOST BINÁRIO - SINAIS DE TRADING
======================================================================

Data de treinamento: 2025-07-11 11:35:33

MODELO BINÁRIO:
  • Tipo: XGBoost Binário (sem classe 'Sem Ação')
  • Classes: 0=Venda, 1=Compra
  • Função de perda: Binary cross-entropy (logloss)
  • Threshold de probabilidade: 0.55 (sinais só gerados se prob > 0.55)
  • Features básicas: pct_change da média OHLC (variação percentual), Volume, Spread, Volatilidade
  • Features econométricas: Parkinson, MFI, EMV, Amihud, Roll Spread,
    Hurst, Vol/Volume, CMF, A/D Line, Volume Oscillator (11 features)
  • Features lagged: 5 lags para cada feature econométrica
  • Total de features: 94
  • Acurácia geral: 0.632

CONFIGURAÇÕES UTILIZADAS:
  • Período de dados: 15y
  • Horizonte de sinais: 1 dias
  • Lags OHLC: 10
  • Lags features econométricas: 5
  • Janela volatilidade: 20
  • Multiplicador spread: 0.5

FEATURES UTILIZADAS (94):
   1. Media_OHLC_PctChange_Lag_1
   2. Media_OHLC_PctChange_Lag_2
   3. Media_OHLC_PctChange_Lag_3
   4. Media_OHLC_PctChange_Lag_4
   5. Media_OHLC_PctChange_Lag_5
   6. Media_OHLC_PctChange_Lag_6
   7. Media_OHLC_PctChange_Lag_7
   8. Media_OHLC_PctChange_Lag_8
   9. Media_OHLC_PctChange_Lag_9
  10. Media_OHLC_PctChange_Lag_10
  11. Volume
  12. Spread
  13. Volatilidade
  14. Parkinson_Volatility
  15. MFI
  16. EMV
  17. EMV_MA
  18. Amihud
  19. Roll_Spread
  20. Hurst
  21. Vol_per_Volume
  22. CMF
  23. AD_Line
  24. VO
  25. Volume_Lag_1
  26. Volume_Lag_2
  27. Volume_Lag_3
  28. Volume_Lag_4
  29. Volume_Lag_5
  30. Spread_Lag_1
  31. Spread_Lag_2
  32. Spread_Lag_3
  33. Spread_Lag_4
  34. Spread_Lag_5
  35. Volatilidade_Lag_1
  36. Volatilidade_Lag_2
  37. Volatilidade_Lag_3
  38. Volatilidade_Lag_4
  39. Volatilidade_Lag_5
  40. Parkinson_Volatility_Lag_1
  41. Parkinson_Volatility_Lag_2
  42. Parkinson_Volatility_Lag_3
  43. Parkinson_Volatility_Lag_4
  44. Parkinson_Volatility_Lag_5
  45. MFI_Lag_1
  46. MFI_Lag_2
  47. MFI_Lag_3
  48. MFI_Lag_4
  49. MFI_Lag_5
  50. EMV_Lag_1
  51. EMV_Lag_2
  52. EMV_Lag_3
  53. EMV_Lag_4
  54. EMV_Lag_5
  55. EMV_MA_Lag_1
  56. EMV_MA_Lag_2
  57. EMV_MA_Lag_3
  58. EMV_MA_Lag_4
  59. EMV_MA_Lag_5
  60. Amihud_Lag_1
  61. Amihud_Lag_2
  62. Amihud_Lag_3
  63. Amihud_Lag_4
  64. Amihud_Lag_5
  65. Roll_Spread_Lag_1
  66. Roll_Spread_Lag_2
  67. Roll_Spread_Lag_3
  68. Roll_Spread_Lag_4
  69. Roll_Spread_Lag_5
  70. Hurst_Lag_1
  71. Hurst_Lag_2
  72. Hurst_Lag_3
  73. Hurst_Lag_4
  74. Hurst_Lag_5
  75. Vol_per_Volume_Lag_1
  76. Vol_per_Volume_Lag_2
  77. Vol_per_Volume_Lag_3
  78. Vol_per_Volume_Lag_4
  79. Vol_per_Volume_Lag_5
  80. CMF_Lag_1
  81. CMF_Lag_2
  82. CMF_Lag_3
  83. CMF_Lag_4
  84. CMF_Lag_5
  85. AD_Line_Lag_1
  86. AD_Line_Lag_2
  87. AD_Line_Lag_3
  88. AD_Line_Lag_4
  89. AD_Line_Lag_5
  90. VO_Lag_1
  91. VO_Lag_2
  92. VO_Lag_3
  93. VO_Lag_4
  94. VO_Lag_5

RESULTADOS DO MODELO:
  • Acurácia Geral: 0.632
  • Distribuição das Predições:
    - Venda: 4200 (48.4%)
    - Compra: 4472 (51.6%)

DEFINIÇÃO DOS SINAIS:
  • Sinal de Compra: Média OHLC atual < Média OHLC 1 dias à frente
  • Sinal de Venda: Média OHLC atual > Média OHLC 1 dias à frente
  • Sem Ação: Casos onde não há sinal de compra nem venda

PARÂMETROS DO XGBOOST:
  • n_estimators: 100
  • max_depth: 6
  • learning_rate: 0.1
  • random_state: 42
  • eval_metric: logloss
  • objective: multi:softprob (adicionado automaticamente)
  • num_class: 3 (adicionado automaticamente)
  • eval_metric: mlogloss (adicionado automaticamente)
